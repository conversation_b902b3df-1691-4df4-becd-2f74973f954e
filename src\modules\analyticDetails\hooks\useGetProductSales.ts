import {useState, useEffect} from 'react'
import {useQuery} from 'react-query'
import {useParams} from 'react-router-dom'
import moment from 'moment'
import {getProductSales} from '../services'
import { dataToProductSale} from '../utils'

const graphTypesOptions = {
  day: [{label: 'Daily', value: 'Day'}],
  week: [
    {label: 'Daily', value: 'Day'},
    {label: 'Weekly', value: 'Week'},
  ],
  month: [
    {label: 'Daily', value: 'Day'},
    {label: 'Weekly', value: 'Week'},
    {label: 'Monthly', value: 'Month'},
  ],
  moreThanMonthLessThanYear: [
    {label: 'Weekly', value: 'Week'},
    {label: 'Monthly', value: 'Month'},
  ],
  year: [{label: 'Yearly', value: 'Year'}],
}

export default function useGetProductSales() {
  const params = useParams<any>()
  const productId = params?.id as string
  const [options, setOptions] = useState(graphTypesOptions['day'])
  const [chartType, setChartType] = useState('Day')

  const [filters, setFilters] = useState({
    product_id: productId,
    start_date: moment().startOf('month').format('YYYY-MM-DD'),
    end_date: moment().format('YYYY-MM-DD'),
    period: 'day',
    state: '',
  })

  const {data, refetch, isFetching} = useQuery(`saleReport`, () => {
    return getProductSales(`/analytics/sold-products/details/aggregated-product-report`, filters)
  })

  useEffect(() => {
    refetch()
  }, [filters, refetch])

  const onDateChange = (startDate: any, endDate: any) => {
    const dayDifference = moment.duration(endDate.diff(startDate)).asDays()
    let period = 'day'

    if (dayDifference <= 7) {
      setOptions(() => graphTypesOptions['day'])
      setChartType('Day')
      period = 'day'
    } else if (dayDifference > 7 && dayDifference <= 31) {
      setOptions(() => graphTypesOptions['week'])
      setChartType('Day')
      period = 'day'
    } else if (dayDifference > 31 && dayDifference <= 365) {
      setOptions(() => graphTypesOptions['moreThanMonthLessThanYear'])
      setChartType('Week')
      period = 'week'
    } else {
      setOptions(() => graphTypesOptions['year'])
      setChartType('Year')
      period = 'year'
    }

    setFilters((prevState) => ({
      ...prevState,
      start_date: startDate.format('YYYY-MM-DD'),
      end_date: endDate.format('YYYY-MM-DD'),
      period: period,
    }))
  }

  const onPeriodChange = (type: string) => {
    setFilters((prevState) => ({
      ...prevState,
      period: type,
    }))
  }

  return {
    data: dataToProductSale(data?.['data'], filters['period']),
    refetch,
    isLoading: isFetching,
    onPeriodChange: onPeriodChange,
    onDateChange: onDateChange,
    setChartType: setChartType,
    options: options,
    chartType: chartType,
    filters: filters,
    setFilters: setFilters,
  }
}
