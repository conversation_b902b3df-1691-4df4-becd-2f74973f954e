import {useQuery} from 'react-query'
import { getPriceReport} from '../services'
import moment from 'moment'
import {useEffect, useState} from 'react'

const defaultStates = {
  start_date: moment().startOf('month').format('YYYY-MM-DD'),
  end_date: moment().format('YYYY-MM-DD'),
}

export const useGetPriceReportData = (productId: any) => {
  const [filters, setFilters] = useState(defaultStates)

  const onDateChange = (startDate: any, endDate: any) => {
    setFilters((prevState) => ({
      ...prevState,
      start_date: startDate?.format('YYYY-MM-DD'),
      end_date: endDate?.format('YYYY-MM-DD'),
    }))
  }

  const {data, isFetching, refetch} = useQuery(
    ['get-PriceReport', filters],
    () => {
      return getPriceReport('/analytics/sold-products/state-wise-stats/price-report', {
        ...filters,
        product_id: productId,
      })
    },
    {
      enabled: !!productId,
    }
  )
  useEffect(() => {
    refetch()
  }, [filters, refetch])
  
  return {
    PriceReportData: data?.data?.data || [],
    PriceReportMeta: data?.data?.meta?.month_rows[0] || {},
    isLoading: isFetching,
    onDateChange,
    filters,
  }
}
