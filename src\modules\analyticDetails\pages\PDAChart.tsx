import useGetProductSales from '../hooks/useGetProductSales'
import {AnalyticsDetailsContext} from '../contexts'
import Header from '../components/Header'
import OverviewCards from '../components/OverviewCards'
import Chart from '../components/Chart'
import {PriceReportTable} from '../sections/table/PriceReportTable'
import {SKUReportTable} from '../sections/table/SKUReportTable'
import MainProductReportNav from '../components/MainProductReportNav'
import {TopCustomersTable} from '../sections/table/TopCustomersTable'

export default function PDAChart() {
  const {data, isLoading, filters, setFilters, onDateChange, onPeriodChange} = useGetProductSales()

  const contextValue = {
    summary: data.summary,
    priceReport: data.price_report,
    productPerformance: data.product_performance,
    productSales: data.product_sales,
    skuReport: data.sku_report,
    skuDistribution: data.sku_distribution,
    variantSalesByCustomers: data.variant_sales_by_customers,
    variantSalesByStates: data.variant_sales_by_states,
    allStates: data.all_states,
    states: data.states,
    stateCount: data.state_count,
    avgPrice: data.avg_price,
    filters,
    setFilters,
    onDateChange,
    onPeriodChange,
    isLoading,
  }

  return (
    <AnalyticsDetailsContext.Provider value={contextValue}>
      <div className='layout-sold-product-inner'>
        <Header />
        <OverviewCards />
        <Chart />
        <PriceReportTable />
        <SKUReportTable />
        <MainProductReportNav />
        <TopCustomersTable />
      </div>
    </AnalyticsDetailsContext.Provider>
  )
}
