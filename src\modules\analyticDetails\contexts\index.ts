import {createContext} from 'react'

export const AnalyticsDetailsContext = createContext({
  summary: null,
  priceReport: null,
  productPerformance: null,
  productSales: null,
  skuReport: null,
  skuDistribution: null,
  variantSalesByCustomers: null,
  variantSalesByStates: null,
  allStates: null,
  states: null,
  stateCount: null,
  avgPrice: null,
  filters: {},
  setFilters: (filters: any) => {},
  onDateChange: (start: any, end: any) => {},
  onPeriodChange: (period: any) => {},
  isLoading: false,
  dateRangeLable: '',
})

export const OrderHistoryPageContext = createContext({
  orderHistoryData: [],
  isLoading: false,
  orderFilters: {},
  onSortingChange: (key: string, value: string) => {},
})

export const PriceChangeLogPageContext = createContext({
  PriceChangeLogData: [],
  isLoading: false,
  priceChangeLogFilters: {},
  onSortingChange: (key: string, value: string) => {},
})

export const ReplenishmentReportPageContext = createContext({
  hasWritePermission: false,
  replenishmentReportList: [] as any,
  isLoading: false,
  filters: {},
  onSearch: (searchValue: string) => {},
  onSortingChange: (field: string, direction: 'asc' | 'desc') => {},
  deleteReplenishmentReport: (id: string) => {},
  isOperationLoading: false,
})