import {useContext} from 'react'
import {AnalyticsDetailsContext} from '../contexts'
import SectionLoading from '../../loading/section-loading'
import LineChart from '../../../components/charts/LineChart'
import TopCustomer from '../sections/TopCustomer'
import TopState from '../sections/TopState'
import TopSku from '../sections/TopSku'
import SKUDistribution from '../sections/SKUDistribution'

const OverviewCards = () => {
  const ctx = useContext(AnalyticsDetailsContext)
  if (!ctx) return null
  const {summary} = ctx
  const summaryData = summary?.data?.[0] || {}
  return (
    <div className='row g-xl-custom-7 mb-10'>
      <div className='col-xxl-4 col-lg-12 mt-0 sold-product-view-cards'>
        <div className='row g-xl-custom-7 d-flex flex-row justify-content-between h-50'>
          <div className='col-6 mt-0'>
            <div className='card border card-flush h-xl-100 h-lg-100'>
              <div className='card-body p-7'>
                <div className=''>
                  <div className='mb-7'>
                    <div className='symbol symbol-50px w-40px h-40px bg-light-primary d-flex align-items-center justify-content-center'>
                      <i className='icon-24'>
                        <KTSVG path='/media/ad-theme-icons/blue-order-icon.svg' />
                      </i>
                    </div>
                  </div>
                  <div className='mb-5'>
                    <h6 className='fs-4 fs-xl-5 fs-lg-6 fs-sm-6 fw-semibold mb-0'>Orders</h6>
                    <span className=' d-flex fs-2hx fs-lg-1 fw-bold'>
                      {parsedOverviewData?.order_count}
                    </span>
                  </div>

                  <span className='fs-6 fw-semibold text-gray-500'>
                    {orders?.map((item, index) => (
                      <div className={'mb-2'} key={index}>
                        <span className='text-dark'>{item.value} </span>
                        {item.type}
                      </div>
                    ))}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className='col-6 mt-0'>
            <div className='card border card-flush h-xl-100 h-lg-100'>
              <div className='card-body p-7'>
                <div className=''>
                  <div className='mb-7'>
                    <div className='symbol symbol-50px w-40px h-40px bg-light-primary d-flex align-items-center justify-content-center'>
                      <i className='icon-24'>
                        <KTSVG path='/media/ad-theme-icons/blue-pause-icon.svg' />
                      </i>
                    </div>
                  </div>
                  <div className='mb-5'>
                    <h6 className='fs-4 fs-xl-5 fs-lg-6 fs-sm-6 fw-semibold mb-0'>Quantity Sold</h6>
                    <span className=' d-flex fs-2hx fs-lg-1 fw-bold'>
                      {parsedOverviewData?.sold_quantity}
                    </span>
                  </div>
                  <span className='fs-6 fw-semibold text-gray-500'>
                    {soldQuantity?.map((item, index) => (
                      <div className={'mb-2'} key={index}>
                        <span className='text-dark'>{item.value} </span>
                        {item.type}
                      </div>
                    ))}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className='card border sold-product-bottom-card'>
          <div className='pt-4'>
            <div className='card-body p-7'>
              <div className=''>
                <div className='d-flex flex-row justify-content-between mb-10'>
                  <div className='symbol symbol-50px w-40px h-40px bg-light-primary d-flex align-items-center justify-content-center'>
                    <i className='icon-24'>
                      <KTSVG path='/media/ad-theme-icons/blue-doller-icon.svg' />
                    </i>
                  </div>
                </div>
                <div className='mb-5'>
                  <h6 className='fs-4 fs-xl-5 fs-lg-6 fs-sm-6 fw-semibold mb-0'>
                    Earning: <strong>{dateRangeLable}</strong>
                  </h6>
                  <span className=' d-flex fs-2hx fs-lg-1 fw-bold '>
                    {parsedOverviewData?.earning}
                  </span>
                </div>
                <span className='fs-6 fw-semibold text-gray-500'>
                  {earnings?.map((item, index) => (
                    <div className={'mb-2'} key={index}>
                      <span className='text-dark'>{item.value} </span>
                      {item.type}
                    </div>
                  ))}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className='col-12 col-md-6 col-xxl-4'>
        <div className='card border card-flush h-100'>
          <div className='card-body p-7'>
            <div className='mb-10'>
              <h3 className='mb-0 fw-bolder'>Product Performance</h3>
              {isLoadingChartData && <SectionLoading />}
            </div>
            <div className='mb-10'>
              {ProductPerformanceData && (
                <LineChart
                  data={dataToLineChart({linedata, linelabel})}
                  options={lineChartOptions}
                />
              )}
            </div>
            <TopCustomer productId={productId} dateRange={dateRange} />
            <TopState productId={productId} dateRange={dateRange} />
            <TopSku productId={productId} dateRange={dateRange} />
          </div>
        </div>
      </div>

      <SKUDistribution
        productId={productId}
        dateRange={dateRange}
        percentages={percentages}
        soldQuantity={soldQuantity}
      />
    </div>
  )
}

export default OverviewCards
