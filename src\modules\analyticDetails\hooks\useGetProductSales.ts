import {useState, useEffect} from 'react'
import {useQuery} from 'react-query'
import {useParams} from 'react-router-dom'
import moment from 'moment'
import {getProductSales} from '../services'
import { dataToProductSale} from '../utils'

const graphTypesOptions = {
  day: [{label: 'Daily', value: 'Day'}],
  week: [
    {label: 'Daily', value: 'Day'},
    {label: 'Weekly', value: 'Week'},
  ],
  month: [
    {label: 'Daily', value: 'Day'},
    {label: 'Weekly', value: 'Week'},
    {label: 'Monthly', value: 'Month'},
  ],
  moreThanMonthLessThanYear: [
    {label: 'Weekly', value: 'Week'},
    {label: 'Monthly', value: 'Month'},
  ],
  year: [{label: 'Yearly', value: 'Year'}],
}

export default function useGetProductSales() {
  const params = useParams<any>()
  const productId = params?.id as string
  const [options, setOptions] = useState(graphTypesOptions['day'])
  const [chartType, setChartType] = useState('Day')

  const [filters, setFilters] = useState({
    product_id: productId,
    start_date: moment().startOf('month').format('YYYY-MM-DD'),
    end_date: moment().format('YYYY-MM-DD'),
    period: 'day',
    state: '',
  })

  const {data, refetch, isFetching} = useQuery(`saleReport`, () => {
    return getProductSales(`/analytics/sold-products/details/aggregated-product-report`, filters)
  })

  useEffect(() => {
    refetch()
  }, [filters, refetch])

  const onDateChange = (startDate: any, endDate: any) => {
    const dayDifference = moment.duration(endDate.diff(startDate)).asDays()
    let period = 'day'

    if (dayDifference <= 7) {
      setOptions(() => graphTypesOptions['day'])
      setChartType('Day')
      period = 'day'
    } else if (dayDifference > 7 && dayDifference <= 31) {
      setOptions(() => graphTypesOptions['week'])
      setChartType('Day')
      period = 'day'
    } else if (dayDifference > 31 && dayDifference <= 365) {
      setOptions(() => graphTypesOptions['moreThanMonthLessThanYear'])
      setChartType('Week')
      period = 'week'
    } else {
      setOptions(() => graphTypesOptions['year'])
      setChartType('Year')
      period = 'year'
    }

    setFilters((prevState) => ({
      ...prevState,
      start_date: startDate.format('YYYY-MM-DD'),
      end_date: endDate.format('YYYY-MM-DD'),
      period: period,
    }))
  }

  const onPeriodChange = (type: string) => {
    setFilters((prevState) => ({
      ...prevState,
      period: type,
    }))
  }

  // Extract data according to endpoint mapping
  const extractedData = {
    // '/analytics/sold-products/state-wise-stats/price-report' => 'price_report'
    PriceReportData: data?.price_report?.data || [],
    PriceReportMeta: data?.price_report?.meta?.month_rows[0] || {},

    // '/analytics/sold-products/state-wise-stats' => 'summary'
    // Pattern from useGetSoldProductOverview: overviewData: response?.data && response?.totals, customerOverviewData: response?.data && response?.data
    summary: data?.summary || {},
    overviewData: data?.summary && data?.summary?.totals,
    customerOverviewData: data?.summary && data?.summary?.data,

    // '/analytics/sold-products/state-wise-stats/variants' => 'sku_report'
    // Pattern from useGetSkuReport: SKUData: data?.data || [], SKUMeta: data?.meta?.month_rows[0] || {}
    SKUData: data?.sku_report?.data || [],
    SKUMeta: data?.sku_report?.meta?.month_rows[0] || {},
    sku_report: data?.sku_report?.data || [],

    // '/analytics/sold-products/state-wise-stats/states' => 'variant_sales_by_states'
    // Pattern from useGetMainProduct: MainProductData: data?.data || [], MainProductMeta: data?.meta?.month_rows[0] || {}
    MainProductData: data?.variant_sales_by_states?.data || [],
    MainProductMeta: data?.variant_sales_by_states?.meta?.month_rows[0] || {},
    variant_sales_by_states: data?.variant_sales_by_states?.data || [],

    // '/analytics/sold-products/state-wise-stats/customers' => 'variant_sales_by_customers'
    // Pattern from useGetTopCustomers: TopCustomerData: data?.data || [], TopCustomerMeta: data?.meta?.month_rows[0] || {}
    TopCustomerData: data?.variant_sales_by_customers?.data || [],
    TopCustomerMeta: data?.variant_sales_by_customers?.meta?.month_rows[0] || {},
    variant_sales_by_customers: data?.variant_sales_by_customers?.data || [],

    // '/analytics/sold-products/state-wise-stats/allstates' => 'all_states'
    // Pattern from useGetAllStateList: AllStateData: response?.data?.states || [], AllStateCount: response?.data?.state_count || 0
    AllStateData: data?.all_states?.data?.states || [],
    AllStateCount: data?.all_states?.data?.state_count || 0,
    all_states: data?.all_states?.data?.states || [],
    state_count: data?.all_states?.data?.state_count || 0,

    // '/analytics/sold-products/state-wise-stats/product-performance' => 'product_performance'
    // Pattern from useGetProductPerformance: ProductPerformanceData: response?.data && response?.data[0]
    ProductPerformanceData: data?.product_performance?.data && data?.product_performance?.data[0],
    product_performance: data?.product_performance?.data?.[0] || {},

    // '/analytics/product-sales' => 'product_sales'
    product_sales: dataToProductSale(data?.product_sales?.data, filters['period']),

    // Additional data from API response
    sku_distribution: data?.sku_report?.sku_distribution || [],
    states: data?.variant_sales_by_states?.data || [],
    avg_price: data?.summary?.avg_price || 0,
  }

  return {
    data: extractedData,
    refetch,
    isLoading: isFetching,
    onPeriodChange: onPeriodChange,
    onDateChange: onDateChange,
    setChartType: setChartType,
    options: options,
    chartType: chartType,
    filters: filters,
    setFilters: setFilters,
  }
}
