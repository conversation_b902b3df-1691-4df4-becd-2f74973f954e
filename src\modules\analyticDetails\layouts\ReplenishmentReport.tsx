import {ReplenishmentReportPageContext} from '../contexts'
import useGetReplenishmentReportList from '../hooks/useGetReplenishmentReportList'
import {Pagination} from '../../../utils/pagination'
import usePermission from '../../../hook/usePermission'
import ReplenishmentReportHeader from '../sections/headers/ReplenishmentReportHeader'
import ReplenishmentReportTable from '../sections/table/ReplenishmentReportTable'

export default function ReplenishmentReport() {
  const {
    replenishmentReportList,
    pagination,
    isLoading,
    filters,
    onSearch,
    onPageChange,
    onSortingChange,
  } = useGetReplenishmentReportList()

  const {hasPermission} = usePermission()
  const hasWritePermission = hasPermission('analytics_replenishment_report', 'write') || false

  const contextValue = {
    hasWritePermission,
    replenishmentReportList,
    isLoading,
    filters,
    onSearch,
    onSortingChange,
  }

  return (
    <ReplenishmentReportPageContext.Provider value={contextValue}>
      <ReplenishmentReportHeader />
      <ReplenishmentReportTable />
      <Pagination pagination={pagination} onPageChange={onPageChange} />
    </ReplenishmentReportPageContext.Provider>
  )
}
