import {Route, Routes, Outlet} from 'react-router-dom'
import {PageTitle, PageLink} from '../../_metronic/layout/core'
import PDANavigation from './components/PDANavigation'
import PDAChart from './pages/PDAChart'

const PDABreadCrumbs: Array<PageLink> = [
  {
    title: 'Analytics',
    path: '/analytics/profitability/profitability-report',
    isSeparator: false,
    isActive: false,
  },
  {
    title: 'Sold Products',
    path: '/analytics/sold-products/product-report',
    isSeparator: false,
    isActive: false,
  },
]

const PDAPage = () => {
  return (
    <>
      <Routes>
        <Route
          element={
            <>
              <PDANavigation />
              <Outlet />
            </>
          }
        >
          <Route
            path=''
            element={
              <>
                <PageTitle breadcrumbs={PDABreadCrumbs}>Product</PageTitle>
                <PDAChart />
              </>
            }
          />
        </Route>
      </Routes>
    </>
  )
}

export default PDAPage
