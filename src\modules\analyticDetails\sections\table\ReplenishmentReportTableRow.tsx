import {getBadgeColor} from '../../../../utils/badge'
import {Link} from 'react-router-dom'

const ReplenishmentReportTableRow = ({row: item, actionOverlay, hasWritePermission}: any) => {
  return (
    <tr>
      <td>
        {hasWritePermission ? (
          <Link
            className='text-hover-primary cursor-pointer text-dark text-decoration-underline'
            to={`/products/${item.id}`} // Placeholder link
          >
            {item?.productName}
          </Link>
        ) : (
          <span>{item?.productName}</span>
        )}
      </td>
      <td>{item?.sku}</td>
      <td>{item?.supplier}</td>
      <td>{item?.stockOnHand}</td>
      <td>{item?.salesVelocity}</td>
      <td>{item?.reorderPoint}</td>
      <td className='text-capitalize'>
        <span className={`badge ${getBadgeColor(item?.status, 'light')} badge-lg`}>
          {item?.status}
        </span>
      </td>
      {/* {hasWritePermission && actionOverlay(ReplenishmentReportActionsMenu)} */}
    </tr>
  )
}

export default ReplenishmentReportTableRow
