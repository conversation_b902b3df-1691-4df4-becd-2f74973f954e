import {useQuery} from 'react-query'
import {getMainProduct} from '../services'
import moment from 'moment'
import {useEffect, useState} from 'react'

const defaultStates = {
  start_date: moment().startOf('month').format('YYYY-MM-DD'),
  end_date: moment().format('YYYY-MM-DD'),
}

export const useGetMainProductData = (productId: any) => {
  const [filters, setFilters] = useState(defaultStates)
  const [topState, setTopState] = useState(null)

  const onDateChange = (startDate: any, endDate: any) => {
    setFilters((prevState) => ({
      ...prevState,
      start_date: startDate?.format('YYYY-MM-DD'),
      end_date: endDate?.format('YYYY-MM-DD'),
    }))
  }

  const {data, isFetching, refetch} = useQuery(
    ['get-MainProduct', filters],
    () => {
      return getMainProduct('/analytics/sold-products/state-wise-stats/states', {
        ...filters,
        product_id: productId,
      })
    },
    {
      enabled: !!productId,
    }
  )
  useEffect(() => {
    refetch()
  }, [filters, refetch])

  useEffect(() => {
    if (data && data?.data && data.data?.[0]?.['state']) {
      setTopState(data.data[0]['state'])
    } else {
      setTopState(null)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, filters.start_date, filters.end_date])
  
  return {
    MainProductData: data?.data || [],
    MainProductMeta: data?.meta?.month_rows[0] || {},
    isLoading: isFetching,
    onDateChange,
    filters,
    topState,
  }
}
