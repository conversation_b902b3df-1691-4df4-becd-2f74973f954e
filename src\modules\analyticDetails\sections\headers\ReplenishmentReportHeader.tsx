import {useContext} from 'react'
import {ReplenishmentReportPageContext} from '../../contexts'
import Search from '../../../../components/Search'

const ReplenishmentReportHeader = () => {
  const {onSearch} = useContext(ReplenishmentReportPageContext)
  return (
    <div className='d-flex justify-content-between align-items-center mb-7'>
      <Search placeholder='Search' onSearch={onSearch} />
    </div>
  )
}

export default ReplenishmentReportHeader
