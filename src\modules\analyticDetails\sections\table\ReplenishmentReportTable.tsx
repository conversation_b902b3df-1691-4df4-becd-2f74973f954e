import {useContext} from 'react'
import DynamicTable from '../../../../components/DynamicTable/DynamicTable'
import { REPLENISHMENT_REPORT_TABLE_COLUMNS } from '../../utils'
import { ReplenishmentReportPageContext } from '../../contexts'
import ReplenishmentReportTableRow from './ReplenishmentReportTableRow'

const ReplenishmentReportTable = () => {
  const {replenishmentReportList, isLoading, onSortingChange, filters, hasWritePermission}: any =
  useContext(ReplenishmentReportPageContext)
  
  return (
    <DynamicTable
      id='kt_table_replenishment_report'
      data={replenishmentReportList}
      sortableColumns={REPLENISHMENT_REPORT_TABLE_COLUMNS}
      TableRow={ReplenishmentReportTableRow}
      loading={isLoading}
      filters={filters}
      onSortingChange={onSortingChange}
      tableClass='table-row-dashed table-row-gray-300 align-middle gs-0 gy-2 mb-15'
      tableHeaderClass='table-row-bordered'
      hasPermission={hasWritePermission}
    />
  )
}

export default ReplenishmentReportTable
