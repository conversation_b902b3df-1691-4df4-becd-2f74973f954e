import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'
import { dataToReplenishmentReport } from '../parsings'

const useGetReplenishmentReportList = () => {
  const Api = useApi()

  const initialFilters = {
    search: '',
    page: 1,
    limit: 30,
    sort_by: 'created_at/-1',
  }

  const {filters, onSearch, onSortingChange, onPageChange} = useFilters(initialFilters, {
    sortType: 'number',
  })

  const {data: response, isFetching} = Api.useGetQuery('/analytics/replenishment-report', {
    queryId: 'replenishment-report-list',
    filters: filters,
    isToast: false,
    isErrorPageRedirection: false,
  })

  const dummyData = [
    {
      id: 1,
      product_name: 'Product 1',
      sku: 'SKU123',
      supplier: 'Supplier 1',
      stock_on_hand: 100,
      sales_velocity: 5,
      reorder_point: 20,
      status: 'OK',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-06-01T12:00:00Z',
    },
    {
      id: 2,
      product_name: 'Product 2',
      sku: 'SKU124',
      supplier: 'Supplier 2',
      stock_on_hand: 80,
      sales_velocity: 3,
      reorder_point: 15,
      status: 'Low',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-06-05T14:00:00Z',
    },
    {
      id: 3,
      product_name: 'Product 3',
      sku: 'SKU125',
      supplier: 'Supplier 3',
      stock_on_hand: 200,
      sales_velocity: 7,
      reorder_point: 30,
      status: 'Critical',
      created_at: '2024-02-01T00:00:00Z',
      updated_at: '2024-06-10T08:30:00Z',
    },
  ]

  return {
    replenishmentReportList: dataToReplenishmentReport(dummyData),
    pagination: {},
    isLoading: false,
    filters,
    onSearch,
    onPageChange,
    onSortingChange,
  }
}

export default useGetReplenishmentReportList
