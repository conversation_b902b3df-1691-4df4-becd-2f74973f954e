export function dataToReplenishmentReport(data: any[]) {
  if (!data || !Array.isArray(data)) return []

  return data.map((item: any) => ({
    id: item['id'],
    productName: item['product_name'] || '-',
    sku: item['sku'] || '-',
    supplier: item['supplier'] || '-',
    stockOnHand: item['stock_on_hand'] || 0,
    salesVelocity: item['sales_velocity'] || 0,
    reorderPoint: item['reorder_point'] || 0,
    status: item['status'] || 'OK',
    createdAt: item['created_at'],
    updatedAt: item['updated_at'],
  }))
}
