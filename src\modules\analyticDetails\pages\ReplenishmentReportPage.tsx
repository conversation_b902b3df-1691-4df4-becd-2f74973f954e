import {Outlet, Route, Routes} from 'react-router-dom'
import {PageLink, PageTitle} from '../../../_metronic/layout/core'
import PDANavigation from '../components/PDANavigation'
import ReplenishmentReport from '../layouts/ReplenishmentReport'

const ReplenishmentPageCrumbs: Array<PageLink> = [
  {
    title: 'Analytics',
    path: '/analytics/replenishment-report',
    isSeparator: false,
    isActive: false,
  },
  {
    title: 'Replenishment',
    path: '/analytics/replenishment-report',
    isSeparator: false,
    isActive: false,
  },
]

const ReplenishmentReportPage = () => {
  return (
    <>
      <Routes>
        <Route
          element={
            <>
              <PDANavigation />
              <Outlet />
            </>
          }
        >
          <Route
            path=''
            element={
              <>
                <PageTitle breadcrumbs={ReplenishmentPageCrumbs}>Replenishment Report</PageTitle>
                <ReplenishmentReport />
              </>
            }
          />
        </Route>
      </Routes>
    </>
  )
}

export default ReplenishmentReportPage
